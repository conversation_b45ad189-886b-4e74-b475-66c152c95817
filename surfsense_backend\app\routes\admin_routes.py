from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List
import uuid

from app.db import get_async_session, User
from app.schemas.admin import AdminUserCreate, AdminUserUpdate, AdminPasswordReset
from app.schemas.users import UserRead
from app.users import current_active_user, get_user_manager
from app.utils.admin_check import check_admin_privileges

router = APIRouter()

@router.post("/users/", response_model=UserRead)
async def admin_create_user(
    user_create: AdminUserCreate,
    user_manager = Depends(get_user_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(check_admin_privileges)
):
    """Create a new user (admin only)"""
    try:
        user = await user_manager.create(user_create, safe=False)
        return user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create user: {str(e)}"
        )

@router.get("/users/", response_model=List[UserRead])
async def admin_list_users(
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(check_admin_privileges)
):
    """List all users (admin only)"""
    result = await session.execute(select(User))
    users = result.scalars().all()
    return users

@router.get("/users/{user_id}", response_model=UserRead)
async def admin_get_user(
    user_id: uuid.UUID,
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(check_admin_privileges)
):
    """Get a specific user by ID (admin only)"""
    result = await session.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user

@router.patch("/users/{user_id}", response_model=UserRead)
async def admin_update_user(
    user_id: uuid.UUID,
    user_update: AdminUserUpdate,
    user_manager = Depends(get_user_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(check_admin_privileges)
):
    """Update a user (admin only)"""
    try:
        result = await session.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        updated_user = await user_manager.update(user_update, user, safe=False)
        return updated_user
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update user: {str(e)}"
        )

@router.delete("/users/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def admin_delete_user(
    user_id: uuid.UUID,
    user_manager = Depends(get_user_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(check_admin_privileges)
):
    """Delete a user (admin only)"""
    try:
        result = await session.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        await user_manager.delete(user)
        return None
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete user: {str(e)}"
        )

@router.post("/users/{user_id}/reset-password", status_code=status.HTTP_200_OK)
async def admin_reset_password(
    user_id: uuid.UUID,
    password_reset: AdminPasswordReset,
    user_manager = Depends(get_user_manager),
    session: AsyncSession = Depends(get_async_session),
    current_user: User = Depends(check_admin_privileges)
):
    """Reset a user's password (admin only)"""
    try:
        result = await session.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        user.hashed_password = user_manager.password_helper.hash(password_reset.password)
        session.add(user)
        await session.commit()
        return {"detail": "Password reset successful"}
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to reset password: {str(e)}"
        )
